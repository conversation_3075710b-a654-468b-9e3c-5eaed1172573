<div class="smart-dashboard-container">
  <!-- Main Layout -->
  <div class="main-layout">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <!-- Dashboard Selection -->
      <div class="dashboard-selection">
        <mat-form-field appearance="outline" class="dashboard-dropdown">
          <mat-label>Select Dashboard</mat-label>
          <mat-select [(value)]="selectedDashboard" [disabled]="!isConfigLoaded" (selectionChange)="onDashboardChange()">
            <mat-option *ngIf="!isConfigLoaded" disabled>Loading...</mat-option>
            <mat-option *ngFor="let dashboardType of dashboardTypes" [value]="dashboardType.value">
              {{dashboardType.label}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <!-- Smart Filters Section -->
      <div class="filters-section">
        <h3 class="filters-title">
          <mat-icon>tune</mat-icon>
          Smart Filters
        </h3>

        <!-- Restaurants Filter -->
        <div class="filter-group">
          <mat-form-field appearance="outline" class="filter-field">
            <!-- Single selection for reconciliation dashboard, multiple for others -->
            <mat-label *ngIf="selectedDashboard === 'reconciliation'">Select restaurant</mat-label>
            <mat-label *ngIf="selectedDashboard !== 'reconciliation'">Select restaurants ({{selectedLocationsCtrl.value?.length || 0}}/{{filteredBranches.length}})</mat-label>

            <mat-select [formControl]="selectedLocationsCtrl" [multiple]="selectedDashboard !== 'reconciliation'">
              <mat-option *ngIf="selectedDashboard !== 'reconciliation'">
                <ngx-mat-select-search
                  [formControl]="locationFilterCtrl"
                  placeholderLabel="Search locations..."
                  noEntriesFoundLabel="No locations found">
                </ngx-mat-select-search>
              </mat-option>
              <!-- Select All / Deselect All Options (only for non-reconciliation dashboards) -->
              <div *ngIf="selectedDashboard !== 'reconciliation'" class="select-all-custom-option" (click)="toggleAllRestaurants($event)">
                <strong>{{areAllRestaurantsSelected() ? 'Deselect All' : 'Select All'}}</strong>
              </div>
              <mat-divider *ngIf="selectedDashboard !== 'reconciliation'"></mat-divider>
              <mat-option *ngFor="let branch of filteredBranches" [value]="branch.restaurantIdOld">
                {{branch.branchName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Work Areas Filter (Non-Reconciliation Dashboards Only) -->
        <div class="filter-group" *ngIf="selectedDashboard !== 'reconciliation'">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select work areas ({{selectedWorkAreasCtrl.value?.length || 0}}/{{getTotalWorkAreasCount()}})</mat-label>

            <mat-select [formControl]="selectedWorkAreasCtrl" multiple>
              <mat-option>
                <ngx-mat-select-search
                  [formControl]="workAreaFilterCtrl"
                  placeholderLabel="Search work areas..."
                  noEntriesFoundLabel="No work areas found">
                </ngx-mat-select-search>
              </mat-option>
              <!-- Select All / Deselect All Options -->
              <div class="select-all-custom-option" (click)="toggleAllWorkAreas($event)">
                <strong>{{areAllWorkAreasSelected() ? 'Deselect All' : 'Select All'}}</strong>
              </div>
              <mat-divider></mat-divider>
              <mat-optgroup *ngFor="let branch of filteredWorkAreas" [label]="branch.branchName">
                <mat-option *ngFor="let workArea of branch.workAreas" [value]="workArea">
                  {{workArea | uppercase}}
                </mat-option>
              </mat-optgroup>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Categories Filter -->
        <div class="filter-group">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select categories ({{selectedCategoriesCtrl.value?.length || 0}}/{{filteredCategories.length}})</mat-label>
            <mat-select [formControl]="selectedCategoriesCtrl" multiple>
              <mat-option>
                <ngx-mat-select-search
                  [formControl]="categoryFilterCtrl"
                  placeholderLabel="Search categories..."
                  noEntriesFoundLabel="No categories found">
                </ngx-mat-select-search>
              </mat-option>
              <!-- Select All / Deselect All Options -->
              <div class="select-all-custom-option" (click)="toggleAllCategories($event)">
                <strong>{{areAllCategoriesSelected() ? 'Deselect All' : 'Select All'}}</strong>
              </div>
              <mat-divider></mat-divider>
              <mat-option *ngFor="let category of filteredCategories; trackBy: trackByIndex" [value]="category">
                {{category}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Subcategories Filter -->
        <div class="filter-group">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select subcategories ({{selectedSubcategoriesCtrl.value?.length || 0}}/{{filteredSubcategories.length}})</mat-label>
            <mat-select [formControl]="selectedSubcategoriesCtrl" multiple>
              <mat-option>
                <ngx-mat-select-search
                  [formControl]="subcategoryFilterCtrl"
                  placeholderLabel="Search subcategories..."
                  noEntriesFoundLabel="No subcategories found">
                </ngx-mat-select-search>
              </mat-option>
              <!-- Select All / Deselect All Options -->
              <div class="select-all-custom-option" (click)="toggleAllSubcategories($event)">
                <strong>{{areAllSubcategoriesSelected() ? 'Deselect All' : 'Select All'}}</strong>
              </div>
              <mat-divider></mat-divider>
              <mat-option *ngFor="let subcategory of filteredSubcategories; trackBy: trackByIndex" [value]="subcategory">
                {{subcategory}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Base Date Filter - Only for Purchase Dashboard -->
        <div class="filter-group" *ngIf="selectedDashboard === 'purchase'">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select base date</mat-label>
            <mat-select [formControl]="baseDateCtrl" [disabled]="!isConfigLoaded">
              <mat-option *ngIf="!isConfigLoaded" disabled>Loading...</mat-option>
              <mat-option *ngFor="let baseDateOption of baseDateOptions" [value]="baseDateOption.value">
                {{baseDateOption.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Date Range Filters -->
        <div class="date-range-group">
          <div class="filter-group">
            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Start Date</mat-label>
              <input matInput [matDatepicker]="startPicker" [formControl]="startDate">
              <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
              <mat-datepicker #startPicker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="filter-group">
            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>End Date</mat-label>
              <input matInput [matDatepicker]="endPicker" [formControl]="endDate">
              <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
              <mat-datepicker #endPicker></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <!-- Kitchen Selection Dropdown (Reconciliation Dashboard Only) -->
        <div class="filter-group" *ngIf="selectedDashboard === 'reconciliation'">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select kitchen mapping</mat-label>
            <mat-select [formControl]="selectedWorkAreasCtrl" multiple>
              <mat-optgroup *ngFor="let branch of filteredWorkAreas" [label]="branch.branchName">
                <mat-option *ngFor="let workArea of branch.workAreas" [value]="workArea">
                  {{workArea | uppercase}}
                </mat-option>
              </mat-optgroup>
            </mat-select>
            <mat-icon matSuffix *ngIf="isKitchenSelected()" class="success-icon">check_circle</mat-icon>
          </mat-form-field>
        </div>

        <!-- Department-Category Mapping Button (Reconciliation Dashboard Only) -->
        <div class="filter-group" *ngIf="selectedDashboard === 'reconciliation'">
          <button mat-stroked-button
                  [color]="isReconciliationMappingConfigured() ? 'primary' : 'warn'"
                  [class.mapping-required]="!isReconciliationMappingConfigured()"
                  (click)="openDepartmentMapping()">
            <mat-icon>{{isReconciliationMappingConfigured() ? 'settings' : 'warning'}}</mat-icon>
            {{isReconciliationMappingConfigured() ? 'Configure Department' : 'Mapping Required'}}
          </button>
        </div>


        <!-- Filter Action Buttons -->
        <div class="filter-actions">
          <button mat-stroked-button class="reset-filters-btn" (click)="resetFilters()">
            <mat-icon>refresh</mat-icon>
            Reset
          </button>
          <button mat-stroked-button color="primary" class="search-btn" (click)="searchDashboard()">
            <mat-icon>search</mat-icon>
            Search
          </button>
        </div>
      </div>

      <!-- Dashboard Mode Toggle -->
      <div class="dashboard-mode-section">
        <h4 class="mode-label">
          <mat-icon>settings</mat-icon>
          Dashboard Mode
        </h4>
        <div class="mode-toggle-container">
          <div class="toggle-switch" [class.ai-disabled]="isAiModeDisabled()">
            <div class="toggle-option"
                 [class.active]="dashboardModeCtrl.value === 'default'"
                 (click)="setDashboardMode('default')">
              <span>Default</span>
            </div>
            <div class="toggle-option ai-option"
                 [class.active]="dashboardModeCtrl.value === 'ask_digi_ai'"
                 [class.disabled]="isAiModeDisabled()"
                 (click)="setDashboardMode('ask_digi_ai')">
              <span>Ask Digi AI</span>
              <mat-icon class="beta-icon" *ngIf="isAiModeDisabled()">🔥</mat-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="right-content">
      <!-- AI Assistant Header -->
      <div class="ai-assistant-header">
        <div class="assistant-info">
          <mat-icon class="assistant-icon disabled">smart_toy</mat-icon>
          <div class="assistant-text">
            <span class="assistant-title">Smart Dashboard Assistant</span>
            <span class="assistant-status disabled">Available Soon</span>
          </div>
        </div>
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-field disabled">
            <input matInput
                   placeholder="Ask me about your business data (Available Soon)"
                   [formControl]="searchQuery"
                   [disabled]="true" />
            <mat-icon matSuffix class="search-icon disabled">search</mat-icon>
          </mat-form-field>
        </div>
      </div>

      <!-- Configuration Warning (Reconciliation Dashboard Only) -->
      <div class="mapping-warning-container"
           *ngIf="selectedDashboard === 'reconciliation' && (!isKitchenSelected() || !isReconciliationMappingConfigured())">
        <div class="mapping-warning">
          <mat-icon class="warning-icon">warning</mat-icon>
          <div class="warning-content">
            <h4>Configuration Required</h4>
            <p *ngIf="!isKitchenSelected() && !isReconciliationMappingConfigured()">
              Please select a kitchen and configure department-category mappings before generating the reconciliation report.
            </p>
            <p *ngIf="!isKitchenSelected() && isReconciliationMappingConfigured()">
              Please select a kitchen before generating the reconciliation report.
            </p>
            <p *ngIf="isKitchenSelected() && !isReconciliationMappingConfigured()">
              Please configure department-category mappings before generating the reconciliation report.
            </p>
          </div>
          <div class="warning-actions">
            <button *ngIf="!isReconciliationMappingConfigured()" mat-raised-button color="warn" (click)="openDepartmentMapping()">
              Configure Mapping
            </button>
          </div>
        </div>
      </div>

      <!-- Dashboard Content Area -->
      <div class="dashboard-content-area">
        <!-- Enhanced Loading State -->
        <div *ngIf="isLoading" class="loading-container">
          <div class="loading-content">
            <!-- Main Loading Animation -->
            <div class="loading-animation">
              <div class="pulse-circle"></div>
              <div class="pulse-circle delay-1"></div>
              <div class="pulse-circle delay-2"></div>
              <mat-icon class="loading-icon">analytics</mat-icon>
            </div>

            <!-- Loading Text -->
            <div class="loading-text">
              <h3>Preparing Your Dashboard</h3>
              <p>Analyzing your {{selectedDashboard}} data and generating insights...</p>
            </div>
          </div>
        </div>

        <!-- Dashboard Grid -->
        <div *ngIf="!isLoading && (summaryCards.length > 0 || charts.length > 0)" class="dashboard-grid">
          <!-- Summary Cards Row -->
          <div *ngIf="summaryCards.length > 0" class="summary-cards-row">
            <mat-card *ngFor="let card of summaryCards" class="summary-card" [style.border-left-color]="card.color">
              <mat-card-content>
                <div class="card-content">
                  <div class="card-icon" [style.color]="card.color">
                    <mat-icon>{{card.icon}}</mat-icon>
                  </div>
                  <div class="card-info">
                    <div class="card-value">{{card.value}}</div>
                    <div class="card-label">{{card.label}}</div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Charts Grid -->
          <div *ngIf="charts.length > 0" class="charts-grid">
            <mat-card *ngFor="let chart of charts; let i = index"
                      class="chart-card"
                      [ngClass]="getChartCssClass(chart)">
              <mat-card-header *ngIf="chart.title">
                <mat-card-title class="chart-title">{{chart.title}}</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="chart-container" [attr.data-chart-type]="chart.type">
                  <!-- Enhanced Table Chart Rendering -->
                  <div *ngIf="chart.type === 'table'" class="table-chart">
                    <div class="table-responsive">
                      <!-- Search and Export Controls -->
                      <div class="table-controls" *ngIf="getTableOptions(chart)?.searchable || getTableOptions(chart)?.exportable">
                        <div class="table-search" *ngIf="getTableOptions(chart)?.searchable">
                          <mat-form-field appearance="outline" class="search-field">
                            <!-- <mat-label>Search</mat-label> -->
                            <input matInput (keyup)="applyTableFilter($event, chart)" placeholder="Search table...">
                            <mat-icon matSuffix>search</mat-icon>
                          </mat-form-field>
                        </div>
                        <div class="table-export" *ngIf="getTableOptions(chart)?.exportable">
                          <button mat-raised-button color="primary" (click)="exportTable(chart)">
                            <mat-icon>download</mat-icon>
                            Export
                          </button>
                        </div>
                      </div>

                      <!-- Material Table -->
                      <mat-table [dataSource]="getTableDataSource(chart)" class="reconciliation-mat-table" style="padding: 4px;">
                        <!-- Dynamic columns -->
                        <ng-container *ngFor="let header of getTableHeaders(chart); let i = index"
                                      [matColumnDef]="'col' + i">
                          <mat-header-cell *matHeaderCellDef>{{header}}</mat-header-cell>
                          <mat-cell *matCellDef="let row">
                            <span [innerHTML]="formatTableCell(row[header], header, row)"
                                  [class.category-cell]="row._isCategory"
                                  [class.grand-total-cell]="row._isGrandTotal">
                            </span>
                          </mat-cell>
                        </ng-container>

                        <mat-header-row *matHeaderRowDef="getDisplayedColumns(chart)"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: getDisplayedColumns(chart);"
                                 [class.category-row]="row._isCategory"
                                 [class.subcategory-row]="row._isSubcategory"
                                 [class.grand-total-row]="row._isGrandTotal"></mat-row>
                      </mat-table>

                      <!-- Pagination -->
                      <mat-paginator *ngIf="getTableOptions(chart)?.showPagination && getTableRows(chart).length > (getTableOptions(chart)?.pageSize || 50)"
                                     [length]="getTableRows(chart).length"
                                     [pageSize]="getTableOptions(chart)?.pageSize || 50"
                                     [pageSizeOptions]="[25, 50, 100, 200]"
                                     showFirstLastButtons>
                      </mat-paginator>
                    </div>
                  </div>

                  <!-- Regular Chart Rendering -->
                  <canvas *ngIf="chart.type !== 'table'" baseChart
                          [type]="getChartType(chart)"
                          [data]="getChartData(chart)"
                          [options]="getChartOptions(chart)">
                  </canvas>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>

        <!-- Single Unified Empty State -->
        <div *ngIf="!isLoading && summaryCards.length === 0 && charts.length === 0" class="empty-state">
          <mat-icon class="empty-icon">analytics</mat-icon>
          <h3>No Data Available</h3>
          <p>Please select filters and click the Search button to view dashboard data.</p>
          <button mat-raised-button color="primary" (click)="searchDashboard()">
            <mat-icon>refresh</mat-icon>
            Refresh
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Department-Category Mapping Dialog -->
<div class="department-mapping-overlay" *ngIf="showDepartmentMapping" (click)="closeDepartmentMapping()">
  <div class="department-mapping-container" (click)="$event.stopPropagation()">
    <div class="mapping-header">
      <h2>
        <mat-icon>settings</mat-icon>
        Department Configuration
      </h2>
      <button mat-icon-button (click)="closeDepartmentMapping()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="mapping-content">
      <!-- Department Selection Section -->
      <div class="department-selection-section">
        <div class="section-header">
          <mat-icon>business</mat-icon>
          <h3>Select Departments</h3>
        </div>

        <!-- Loading State -->
        <div *ngIf="departments.length === 0" class="loading-state">
          <mat-spinner diameter="24"></mat-spinner>
          <p>Loading departments...</p>
        </div>

        <!-- Department Selection -->
        <mat-form-field *ngIf="departments.length > 0" appearance="outline" class="department-filter-field">
          <mat-label>Select departments ({{selectedDepartmentsCtrl.value?.length || 0}}/{{filteredDepartments.length}})</mat-label>
          <mat-select [formControl]="selectedDepartmentsCtrl" multiple>
            <mat-option>
              <ngx-mat-select-search
                [formControl]="departmentFilterCtrl"
                placeholderLabel="Search departments..."
                noEntriesFoundLabel="No departments found">
              </ngx-mat-select-search>
            </mat-option>
            <!-- Select All / Deselect All Options -->
            <div class="select-all-custom-option" (click)="toggleAllDepartments($event)">
              <strong>{{areAllDepartmentsSelected() ? 'Deselect All' : 'Select All'}}</strong>
            </div>
            <mat-divider></mat-divider>
            <mat-option *ngFor="let department of filteredDepartments; trackBy: trackByIndex" [value]="department.id">
              {{department.name}} <span *ngIf="department.code">({{department.code}})</span>
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Category Mapping Section -->
      <div class="category-mapping-section">
        <div class="section-header">
          <mat-icon>category</mat-icon>
          <h3>Configure Category Mappings</h3>
        </div>
        <app-department-category-mapping
          [departments]="departments"
          [selectedDepartmentIds]="selectedDepartmentsCtrl.value || []"
          [categories]="categories"
          [existingMappings]="departmentCategoryMappings"
          [tenantId]="user?.tenantId"
          [showAsDialog]="true"
          (mappingsChanged)="onDepartmentMappingsChanged($event)">
        </app-department-category-mapping>
      </div>
    </div>
  </div>
</div>
