"""
Smart Dashboard Router - LLM-powered dashboard generation
"""
import json
import os
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv

from app.utility.report import grnStatusReport, store_variance, inventoryConsumptionNew
from app.utility.dashboard_agents import (
    smart_ask_dashboard,
    generate_purchase_dashboard,
    generate_inventory_dashboard,
    generate_reconciliation_dashboard
)
from app.database import roloposconfigsCol
import requests

load_dotenv()

# ===== ROUTER SETUP =====
router = APIRouter()
security = HTTPBearer()

# ===== AUTHENTICATION =====
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Authenticate requests using Bearer token"""
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")

    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")

    return credentials.credentials

# ===== CONFIGURATION =====
DASHBOARD_CONFIG = {
    "chart_colors": [
        '#ff8c42',  # Orange primary
        '#ffb366',  # Orange light
        '#87a96b',  # Sage green
        '#6b9bd2',  # Soft blue
        '#9b7bb8',  # Muted purple
        '#8d7b68',  # Warm gray
        '#d4a5a5',  # Dusty rose
        '#ffc999',  # Orange lighter
        '#a4c085',  # Sage green light
        '#85aedb',  # Soft blue light
        '#af95c6',  # Muted purple light
        '#a69082',  # Warm gray light
        '#ddb8b8',  # Dusty rose light
        '#ffe0cc',  # Orange lightest
        '#f4a261'   # Warning orange
    ],
    "chart_types": {
        "bar": "Bar Chart",
        "horizontalBar": "Horizontal Bar Chart",
        "line": "Line Chart",
        "doughnut": "Doughnut Chart",
        "pie": "Pie Chart",
        "radar": "Radar Chart",
        "polarArea": "Polar Area Chart"
    },
    "currency": {"code": "INR", "symbol": "₹"},
    "dashboard_types": [
        {"value": "inventory", "label": "Inventory Dashboard"},
        {"value": "purchase", "label": "Purchase Dashboard"},
        {"value": "reconciliation", "label": "COGS Dashboard"},
    ],
    "base_date_options": [
        {"value": "deliveryDate", "label": "GRN Date(System Entry Date)"},
        {"value": "invoiceDate", "label": "Vendor Invoice Date"},
        {"value": "grnDate", "label": "Goods Received Date"}
    ],
    "default_chart_options": {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "legend": {
                "display": True,
                "position": "top",
                "labels": {
                    "usePointStyle": True,
                    "padding": 15,
                    "font": {"size": 11}
                }
            },
            "tooltip": {
                "backgroundColor": "rgba(255, 255, 255, 0.95)",
                "titleColor": "#333",
                "bodyColor": "#666",
                "borderColor": "#ffb366",
                "borderWidth": 2,
                "cornerRadius": 6
            }
        },
        "scales": {
            "x": {
                "grid": {"display": False},
                "ticks": {"font": {"size": 10}}
            },
            "y": {
                "beginAtZero": True,
                "grid": {"color": "#e9ecef"},
                "ticks": {"font": {"size": 10}}
            }
        }
    },
    "default_chart_options": {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "legend": {
                "display": True,
                "position": "top",
                "labels": {
                    "usePointStyle": True,
                    "padding": 15,
                    "font": {"size": 11}
                }
            },
            "tooltip": {
                "backgroundColor": "rgba(255, 255, 255, 0.95)",
                "titleColor": "#333",
                "bodyColor": "#666",
                "borderColor": "#ffb366",
                "borderWidth": 2,
                "cornerRadius": 6
            }
        },
        "scales": {
            "x": {
                "grid": {"display": False},
                "ticks": {"font": {"size": 10}}
            },
            "y": {
                "beginAtZero": True,
                "grid": {"color": "#e9ecef"},
                "ticks": {"font": {"size": 10}}
            }
        }
    },
    "summary_card_config": {
        "colors": {
            "currency": "#ffb366",
            "number": "#ff9d4d",
            "percentage": "#ffc999",
            "text": "#6c757d"
        },
        "icons": {
            "currency": "account_balance_wallet",
            "number": "analytics",
            "percentage": "percent",
            "text": "info"
        }
    },
    "ui_config": {
        "default_date_range_days": 30,
        "default_dashboard_type": "inventory",
        "default_base_date": "deliveryDate"
    }
}

# ===== ROUTES =====
@router.get("/config")
async def get_dashboard_config(_: str = Depends(authenticate)) -> Dict[str, Any]:
    """Get global dashboard configuration for dynamic frontend rendering"""
    return {"status": "success", "data": DASHBOARD_CONFIG}



@router.get("/departments/{tenant_id}")
async def get_departments(tenant_id: str, _: str = Depends(authenticate)) -> Dict[str, Any]:
    """Get departments from RMS API for a tenant (secure backend implementation)"""

    # Fetch tenant credentials from MongoDB
    config = roloposconfigsCol.find_one({"tenantId": tenant_id})
    if not config:
        return {"status": "error", "message": "Tenant configuration not found"}

    # Extract RMS API credentials (stored securely in backend)
    credentials = {
        "emailId": config.get('emailId'),
        "password": config.get('password')
    }

    # Authenticate with RMS API
    rms_api_url = "https://rms-api.digitory.com"
    login_response = requests.post(
        f"{rms_api_url}/login",
        json=credentials,
        headers={"Content-Type": "application/json", "App-Id": "inventory"},
        timeout=30
    )

    if login_response.status_code != 200:
        return {"status": "error", "message": "RMS API authentication failed"}

    auth_data = login_response.json()
    print(auth_data)
    token = auth_data.get("loggedInEmployee").get("token")
    account_id = auth_data.get("loggedInEmployee").get("accountID")

    if not token or not account_id:
        print('...................................')
        print(token, account_id)
        return {"status": "error", "message": "Invalid RMS API authentication response"}

    # Fetch departments from RMS API
    departments_response = requests.get(
        f"{rms_api_url}/account/{account_id}/departments",
        headers={
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        },
        timeout=30
    )

    if departments_response.status_code != 200:
        return {"status": "error", "message": "Failed to fetch departments from RMS API"}

    departments_data = departments_response.json()

    # Format departments for frontend
    departments = []
    if isinstance(departments_data, list):
        for dept in departments_data:
            departments.append({
                "id": dept.get("id"),
                "name": dept.get("name")
            })

    return {"status": "success", "data": departments}





@router.post("/smart_ask")
async def smart_ask(request: Dict[str, Any], _: str = Depends(authenticate)) -> Dict[str, Any]:
    """Generate smart dashboard based on user query and filters"""
    try:
        # Extract request parameters
        filters = request.get('filters', {})
        user_query = request.get('user_query', '')
        tenant_id = request.get('tenant_id', '')
        use_default_charts = request.get('use_default_charts', False)
        dashboard_type = request.get('dashboard_type', 'purchase')
        department_category_mappings = request.get('department_category_mappings', [])

        # Validate department-category mappings for reconciliation dashboard
        if dashboard_type == 'reconciliation':
            if not department_category_mappings or len(department_category_mappings) == 0:
                return {
                    "status": "error",
                    "message": "Department-category mappings are required for reconciliation dashboard",
                    "error_code": "MAPPING_REQUIRED"
                }

            # Validate that at least one mapping has categories
            has_valid_mappings = any(
                mapping.get('categories') and len(mapping.get('categories', [])) > 0
                for mapping in department_category_mappings
            )

            if not has_valid_mappings:
                return {
                    "status": "error",
                    "message": "At least one department must have categories mapped for reconciliation dashboard",
                    "error_code": "INVALID_MAPPING"
                }

        # Build job configuration
        job = _build_job_config(tenant_id, filters)

        # Generate dashboard based on type
        dashboard_data = _generate_dashboard(job, dashboard_type, user_query, use_default_charts, tenant_id, department_category_mappings)

        return {"status": "success", "data": dashboard_data}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid request data: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# ===== HELPER FUNCTIONS =====
def _build_job_config(tenant_id: str, filters: Dict[str, Any]) -> Dict[str, Any]:
    """Build job configuration from request parameters"""
    try:
        return {
            'tenantId': tenant_id,
            'details': {
                'selectedRestaurants': filters.get('locations', []),
                'selectedWorkAreas': filters.get('workAreas', []),
                'selectedBaseDate': filters.get('baseDate', 'deliveryDate'),
                'selectedCategories': filters.get('categories', []),
                'selectedSubCategories': filters.get('subcategories', []),
                'selectedDepartments': filters.get('departments', []),
                'startDate': datetime.strptime(filters.get('startDate'), '%Y-%m-%d'),
                'endDate': datetime.strptime(filters.get('endDate'), '%Y-%m-%d')
            }
        }
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid date format in filters: {str(e)}")

def _generate_dashboard(job: Dict[str, Any], dashboard_type: str, user_query: str, use_default_charts: bool, tenant_id: str = '', department_category_mappings: list = None) -> Dict[str, Any]:
    """Generate dashboard data based on type"""
    if dashboard_type == 'purchase':
        selected_categories = job['details'].get('selectedCategories', [])
        selected_subcategories = job['details'].get('selectedSubCategories', [])
        selected_work_areas = job['details'].get('selectedWorkAreas', [])

        job['details'].update({
            'selectedCategories': selected_categories if selected_categories else ['all'],
            'selectedSubCategories': selected_subcategories if selected_subcategories else ['all'],
            'selectedWorkAreas': selected_work_areas if selected_work_areas else ['all']
        })

        df = grnStatusReport(job)
        return generate_purchase_dashboard(df)

    elif dashboard_type == 'inventory':
        selected_categories = job['details'].get('selectedCategories', [])
        selected_subcategories = job['details'].get('selectedSubCategories', [])
        selected_work_areas = job['details'].get('selectedWorkAreas', [])

        job['details'].update({
            'selectedCategories': selected_categories if selected_categories else ['all'],
            'selectedSubCategories': selected_subcategories if selected_subcategories else ['all'],
            'selectedVendors': [],
            'selectedWorkAreas': selected_work_areas if selected_work_areas else ['all'],
            'type': 'store_variance'
        })
        df = store_variance(job)
        return generate_inventory_dashboard(df)

    elif dashboard_type == 'reconciliation':
        selected_categories = job['details'].get('selectedCategories', [])
        selected_subcategories = job['details'].get('selectedSubCategories', [])
        selected_work_areas = [job['details'].get('selectedWorkAreas', [])[-1]]
        selected_departments = job['details'].get('selectedDepartments', [])

        job['details'].update({
            'selectedCategories': selected_categories if selected_categories else ['all'],
            'selectedSubCategories': selected_subcategories if selected_subcategories else ['all'],
            'selectedVendors': [],
            'selectedWorkAreas': selected_work_areas if selected_work_areas else ['all'],
            'selectedDepartments': selected_departments if selected_departments else ['all'],
            'type': 'reconciliation'
        })

        # Get both store_variance and inventory_consumption data
        store_variance_df = store_variance(job)
        inventory_consumption_df = inventoryConsumptionNew(job)
        print(len(store_variance_df), "store_variance_df")
        print(len(inventory_consumption_df), "inventory_consumption_df")
        print(f"Selected departments: {selected_departments}")

        return generate_reconciliation_dashboard(
            store_variance_df,
            inventory_consumption_df,
            departments=selected_departments,
            tenant_id=tenant_id,
            department_category_mappings=department_category_mappings
        )

    else:
        # TODO: Add support for other dashboard types - smart_ask_dashboard(df, user_query, use_default_charts)
        raise ValueError(f"Invalid dashboard type: {dashboard_type}")
